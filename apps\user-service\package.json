{"name": "user-service", "type": "module", "scripts": {"dev": "vite", "build": "esbuild --bundle --outfile=./dist/index.js --platform=node --target=node20 ./src/index.ts", "zip": "zip -j lambda.zip dist/index.js", "update": "aws lambda update-function-code --zip-file fileb://lambda.zip --function-name hello", "deploy": "run-s build zip update", "lint": "eslint . --fix", "check-types": "tsc --noEmit"}, "devDependencies": {"@repo/eslint-config": "workspace:*", "@repo/typescript-config": "workspace:*", "@types/node": "^22.0.0", "esbuild": "^0.25.6", "npm-run-all2": "^8.0.4", "vite": "^7.0.4", "@hono/vite-dev-server": "^0.20.0", "eslint": "^9.30.1", "prettier": "^3.6.2", "typescript": "^5.8.3", "vite-tsconfig-paths": "^5.1.4"}, "dependencies": {"hono": "^4.8.4"}}