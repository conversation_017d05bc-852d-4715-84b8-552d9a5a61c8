import { tanstackStartConfig } from "@repo/eslint-config/tanstack-start";

export default [

  ...tanstackStartConfig,
  {
    languageOptions: {
      parserOptions: {
        tsconfigRootDir: import.meta.dirname,
      },
    },
  },
  {
    settings: {
      'import/resolver': {
        // Add alias resolver for @/ path specific to this app
        alias: {
          map: [
            ['@', './src'],
          ],
          extensions: ['.ts', '.tsx', '.js', '.jsx', '.json'],
        },
      },
    },
  },
  {
    // Ignore linting for UI components folder
    ignores: ['src/components/ui/**/*'],
  },
];
