{"name": "sylanis", "private": true, "scripts": {"build": "turbo run build", "dev": "turbo run dev", "lint": "turbo run lint", "format": "prettier --write \"**/*.{ts,tsx,md}\"", "check-types": "turbo run check-types", "check": "pnpm lint && pnpm check-types"}, "devDependencies": {"prettier": "^3.6.2", "turbo": "^2.5.4", "typescript": "5.8.3"}, "packageManager": "pnpm@10.13.1", "engines": {"node": ">=18"}}