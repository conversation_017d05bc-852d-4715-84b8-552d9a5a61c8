import js from '@eslint/js'
import prettier from 'eslint-config-prettier'
import importPlugin from 'eslint-plugin-import'
// @ts-expect-error - Package doesn't have type declarations
import onlyWarn from 'eslint-plugin-only-warn'
import turboPlugin from 'eslint-plugin-turbo'
import tseslint from 'typescript-eslint'

export const baseConfig: unknown[] = [
  js.configs.recommended,
  prettier,
  ...tseslint.configs.recommended,
  {
    languageOptions: {
      parserOptions: {
        warnOnUnsupportedTypeScriptVersion: false,
        sourceType: 'module',
        ecmaVersion: 'latest',
        project: './tsconfig.json',
        tsconfigRootDir: '.',
      },
    },
  },
  {
    rules: {
      'consistent-return': 'error',
      '@typescript-eslint/no-unused-vars': [
        1,
        {
          argsIgnorePattern: '^_',
          varsIgnorePattern: '^_',
        },
      ],
      '@typescript-eslint/no-namespace': 'off',
      // Ensure promises are properly handled
      'no-return-await': 'off', // Turn off base rule (recommended when using typescript-eslint)
    },
  },
  // -----------------------------------------------
  // TypeScript-specific rules (requiring type info)
  // -----------------------------------------------
  {
    files: ['**/*.ts', '**/*.tsx'],
    rules: {
      '@typescript-eslint/no-floating-promises': 'error',
      '@typescript-eslint/return-await': 'error', // Better alternative to no-return-await
      // Consider adding these useful type-aware rules (was giving false warnings)
      // '@typescript-eslint/no-unnecessary-condition': 'warn',
      // '@typescript-eslint/no-unnecessary-type-assertion': 'warn',
    },
  },
  importPlugin.flatConfigs.recommended,
  {
    rules: {
      'import/no-unresolved': ['error', { caseSensitive: false }],
      'import/order': [
        'error',
        {
          groups: ['builtin', 'external', 'internal', 'parent', 'sibling', 'index', 'object', 'type'],
          'newlines-between': 'always', // Add separation between import groups
          alphabetize: { order: 'asc', caseInsensitive: true }, // Sort imports alphabetically
        },
      ],
    },
    settings: {
      'import/resolver': {
        // You will also need to install and configure the TypeScript resolver
        // See also https://github.com/import-js/eslint-import-resolver-typescript#configuration
        typescript: {
          alwaysTryTypes: true, // always try to resolve types under `<root>@types` directory even it doesn't contain any source code, like `@types/unist`
          project: './tsconfig.json',
          extensions: ['.ts', '.tsx', '.js', '.jsx', '.json'],
        },
      },
    },
  },
  // -----------------------------------------------
  // Default export rules exceptions
  // -----------------------------------------------
  {
    ignores: ['eslint.config.ts', 'postcss.config.ts', 'prettier.config.ts', 'vite.config.ts', '**/index.ts'],
    rules: {
      'import/no-default-export': 'error',
    },
  },
  {
    plugins: {
      turbo: turboPlugin,
    },
    rules: {
      'turbo/no-undeclared-env-vars': 'warn',
    },
  },
  {
    plugins: {
      onlyWarn,
    },
  },
  {
    ignores: ['dist/**', 'prettier.js', 'prettier.config.js'],
  },
]
