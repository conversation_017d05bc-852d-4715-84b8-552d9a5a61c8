{"name": "@repo/eslint-config", "version": "0.0.0", "type": "module", "private": true, "exports": {"./base": "./base.ts", "./react-internal": "./react-internal.ts", "./hono-service": "./hono-service.ts", "./tanstack-start": "./tanstack-start.ts", "./prettier": "./prettier.ts"}, "scripts": {"lint": "eslint . --max-warnings 0 --fix", "check-types": "tsc --noEmit"}, "devDependencies": {"@repo/typescript-config": "workspace:*", "@eslint/js": "^9.30.0", "eslint": "^9.30.0", "eslint-config-prettier": "^10.1.1", "eslint-plugin-only-warn": "^1.1.0", "eslint-plugin-import": "^2.32.0", "eslint-import-resolver-typescript": "^4.4.4", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "@tanstack/eslint-plugin-query": "5.81.2", "eslint-plugin-turbo": "^2.5.0", "globals": "^16.2.0", "typescript": "^5.8.2", "typescript-eslint": "^8.35.0"}}