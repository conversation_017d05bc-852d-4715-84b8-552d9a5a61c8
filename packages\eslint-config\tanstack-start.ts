import pluginQuery from '@tanstack/eslint-plugin-query'
import pluginReact from "eslint-plugin-react";
import pluginReactHooks from "eslint-plugin-react-hooks";
import reactRefresh from 'eslint-plugin-react-refresh'
import globals from "globals";
import { ConfigArray } from "typescript-eslint";

import { baseConfig } from "./base";

export const tanstackStartConfig = [
  ...baseConfig,
  {
    ...pluginReact.configs.flat.recommended!,
    languageOptions: {
      ...pluginReact.configs.flat.recommended!.languageOptions,
      globals: {
        ...globals.serviceworker,
        ...globals.browser,
      },
    },
  },
  {
    plugins: {
      "react-hooks": pluginReactHooks,
    },
    settings: { react: { version: "detect" } },
    rules: {
      ...pluginReactHooks.configs.recommended.rules,
      // React scope no longer necessary with new JSX transform.
      "react/react-in-jsx-scope": "off",
      'react/jsx-uses-react': 'off', // Disabled for new JSX transform
    },
  },
  {
    ...reactRefresh.configs.recommended,
  },
  // Vite-specific configuration for TanStack Start
  {
    rules: {
      "import/no-unresolved": [
        'error',
        {
          caseSensitive: false,
          // Ignore Vite import suffixes commonly used in TanStack Start projects
          ignore: [
            '\\?url$',
            '\\?raw$',
            '\\?inline$',
            '\\?worker$',
            '\\?worker&inline$',
            '\\?sharedworker$',
            '\\?sharedworker&inline$'
          ]
        }
      ],
    },
  },
  ...pluginQuery.configs['flat/recommended'],
  pluginReact.configs.flat['jsx-runtime'] as ConfigArray[number]
];
